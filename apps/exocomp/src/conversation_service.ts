import {
  status as grpcStatus,
  sendUnaryData,
  ServerUnaryCall,
  ServerWritableStream,
} from '@grpc/grpc-js';
import { KnownBlock } from '@slack/types';
import { getUserRoles } from '@tribble/auth-helper';
import { streamChatCompletion } from '@tribble/chat-completion-service';
import {
  ChatCompletionParams,
  ChatCompletionResponse,
  Tool,
} from '@tribble/chat-completion-service/client';
import { isReasoningModel, readVersionFile } from '@tribble/common-utils';
import {
  AnswerUpdate,
  Cartridge as CartridgeEnum,
  ClientType,
  ConversationMessageRequest,
  ConversationMessageResponse,
  ConversationRequest,
  ConversationResponse,
  IConversationServiceServer,
  ToolStatusUpdate,
  VersionRequest,
  VersionResponse,
} from '@tribble/conversation-service';
import { FormRecognizedDoc } from '@tribble/formrecognizer';
import { getHubSpotConnectionForUser, HubSpotClient } from '@tribble/hubspot';
import { getSalesforceConnectionForUser } from '@tribble/salesforce';
import { ClientSchema } from '@tribble/tribble-db';
import { tableNames } from '@tribble/tribble-db/constants';
import {
  CartridgeId,
  ClientId,
  ConversationDetailId,
  ConversationId,
  UserId,
} from '@tribble/tribble-db/types';
import { ResponseFormat } from '@tribble/types-shared';
import { Connection as SalesforceConnection } from 'jsforce';
import { v4 as uuidv4 } from 'uuid';
import { insertActivityLog } from './activity_log.ts';
import { AgentResponse } from './agent_response.ts';
import { logger } from './app.ts';
import { SecretClient } from './azure.ts';

import { Cartridge } from './cartridges/cartridge.ts';
import {
  CommonCartridgeArgs,
  getCartridge,
  getCartridgeByEnum,
} from './cartridges/index.ts';

import { get_encoding } from 'tiktoken';
import {
  Client,
  getClientBySchema,
  getClientDocumentTypes,
  getClientSettings,
} from './clients.ts';
import {
  Conversation,
  ConversationSystem,
  getConversationById,
  getConversationRecordByMessageIdAndChannel,
  insertNewConversation,
} from './conversations/conversation.ts';
import { ConversationSourceFormat } from './conversations/conversation_context.ts';
import {
  attachConversationDetailEmbeddings,
  completeConversationDetail,
  createConversationDetail,
  insertConversationDetail,
  updateConversationDetail,
} from './conversations/conversation_detail.ts';
import { getBidPacketFilesForConversation } from './conversations/conversation_files.ts';
import {
  insertNewConversationState,
  updateConversationStateTimeFilter,
} from './conversations/conversation_state.ts';
import {
  extendConversationLock,
  tryConversationLock,
  tryReleaseConversationLock,
} from './conversations/lock.ts';
import {
  consumeMessageQueue,
  ConversationImageUrlContentPart,
  ConversationMessage,
  ConversationMessageToolCall,
  ConversationTextContentPart,
  getEagerRagToolResults,
  getMessagesAfterSeq,
  introspectMessages,
  queueConversationMessage,
} from './conversations/message.ts';
import { getTagsForConversation } from './conversations/slack_tags.ts';
import { selectValidEmbeddings } from './embeddings.ts';
import { MetadataFilterValue } from './metadata_filters.ts';
import { getResolvedSettings, ResolvedSettings } from './resolved_settings.ts';
import { telemetryClient } from './telemetry.ts';
import {
  BrainSearch,
  ExtractTemporal,
  GenerateEphemeralUI,
  GenerateResultUI,
  LearnFact,
  StructuredQuery,
  TieredContextSearchTool,
  WebSearch,
} from './tools/index.ts';
import { createToolInstance, ToolCommonConfig } from './tools/toolbox.ts';
import { getUserById, getUserSettings, User } from './users.ts';

const encoding = get_encoding('cl100k_base');

const constants = {
  AUTH_ID: 'auth_id',
  ACCOUNT_ID: 'account_id',
  SECRET_HELPER_FUNC_KEY: 'FUNCKEY-HELPER',

  ERROR_CODE_CONTEXT_LENGTH: 'context_length_exceeded',

  EVENT_LLM_CHAT_CALL: 'llm_chat_call',
  EVENT_AGENT_LOCK_LOOP: 'agent_lock_loop',
  ERROR_13_INVALID_PROMPT: 'Error: 13 INTERNAL: invalid_prompt:',
};

const agentApiVersionLatest =
    process.env.AGENT_API_VERSION_LATEST ?? '2024-10-21',
  agentModel = process.env.AGENT_MODEL ?? 'gpt-4.1',
  agentTemp = parseFloat(process.env.AGENT_TEMP),
  coworkerMessageName = 'Coworker',
  historyMaxLength = parseInt(process.env.HISTORY_MAX_LENGTH, 10),
  forceSummaryIfThisManyTokens = parseInt(
    process.env.FORCE_SUMMARY_TOKENS ?? '100000',
    10,
  ),
  globalContextLengthMaximum = parseInt(
    process.env.GLOBAL_CONTEXT_LENGTH_MAXIMUM || '180000',
    10,
  ); //Protects against huge tool responses.

export type ActivityLogData = {
  client_id?: number;
  user_id?: number;
  details?: string;
  event?: string;
  source_table: string;
  source_id: string;
  type: string;
};

interface SlackTelemetry {
  clientId: ClientId;
  clientName: string;
  correlationId: string;
  isThread: boolean;
  isDM: boolean;
  botMentioned: boolean;
}

const secretClient = new SecretClient(),
  helperFuncKey = await secretClient.getSecret(
    constants.SECRET_HELPER_FUNC_KEY,
  ),
  posiHelpersUrl =
    process.env.POSITRONIC_HELPER_URL ||
    (await secretClient.getSecret('HELPER-ENDPOINT')),
  pureMDToken = await secretClient.getSecret('PURE-MD-TOKEN');

export const conversationServer: IConversationServiceServer = {
  findOrCreateConversation: async (
    call: ServerUnaryCall<ConversationRequest, ConversationResponse>,
    callback: sendUnaryData<ConversationResponse>,
  ) => {
    const req = call.request,
      schema = req.getSchema(),
      channel = req.getChannel(),
      messageId = req.getMessageId();

    logDebugMessage(
      `[findOrCreateConversation] Request: ${JSON.stringify(
        req.toObject(),
        null,
        2,
      )}`,
    );

    try {
      const conversation = await getConversationRecordByMessageIdAndChannel(
        schema as ClientSchema,
        channel,
        messageId,
      );
      if (conversation) {
        const resp = new ConversationResponse();
        resp.setConversationId(conversation.id.toString());
        callback(null, resp);
        return;
      }

      // Create a new conversation record.
      const { id } = await insertNewConversation(schema, {
          channel,
          message_id: messageId,
          system: clientTypeToString(req.getClientType()),
          user_id: req.getUserId() as UserId,
        }),
        conversationId = id;

      console.log(
        `[findOrCreateConversation] Conversation not found for messageId=[${messageId}]. Created new conversation id=[${id}]`,
      );

      // Create a conversation state record. States include things like
      // metadata_filters.
      await insertNewConversationState(schema, {
        conversation_id: parseInt(conversationId),
        metadata_filter: req.getMetadataFilterIdsList(),
      });

      const resp = new ConversationResponse();
      resp.setConversationId(conversationId);
      resp.setIsNew(true);
      callback(null, resp);
    } catch (err) {
      console.error('Error in findOrCreateConversation:', err);
      callback(err, null);
    }
  },

  sendMessage: async (
    call: ServerWritableStream<
      ConversationMessageRequest,
      ConversationMessageResponse
    >,
  ) => {
    const req = call.request,
      correlationId = req.getCorrelationId() || uuidv4(),
      promptOverride = req.getPromptOverride();
    let cartridgeName = req.getCartridge();

    logDebugMessage(
      `(${correlationId}): [sendMessage] Request: ${JSON.stringify(
        req.toObject(),
        null,
        2,
      )}`,
    );

    let client: Client,
      user: User,
      bidPacketFiles: FormRecognizedDoc[],
      exemplarFiles: FormRecognizedDoc[];
    try {
      const schema = req.getSchema() as ClientSchema;
      user = await getUserById(req.getUserId());

      const userSettings = await getUserSettings(schema, user.id);
      const clientSettings = await getClientSettings(schema);
      const settings = await getResolvedSettings(
        schema,
        userSettings,
        clientSettings,
      );
      const timeout = req.hasTimeout()
        ? req.getTimeout().getSeconds() * 1000
        : 30000;

      const documentTypes = await getClientDocumentTypes(schema);

      const webSearchSites = settings.agentWebSearchAllowlist
          .split('\n')
          .map(extractDomainName)
          .filter(Boolean),
        userRoles = await getUserRoles(user.email, user.id),
        isContentMod = userRoles.some(
          (r) => r.name.toLowerCase() === 'content moderator',
        );

      let salesforceConn: SalesforceConnection;
      try {
        salesforceConn = await getSalesforceConnectionForUser(schema, user.id);
      } catch (err) {
        console.warn('Error fetching Salesforce conn:', err);
      }

      let hubspotClient;
      try {
        hubspotClient = await getHubSpotConnectionForUser(schema, user.id);
      } catch (err) {
        console.warn('Error fetching HubSpot conn:', err);
      }

      let conversation: Conversation;
      try {
        (conversation = await getConversationById(
          schema,
          req.getConversationId() as ConversationId,
        )),
          (client = await getClientBySchema(schema));

        await conversation.loadHistory();
      } catch (err) {
        console.error(
          `(${correlationId}): [sendMessage] Error loading conversation: ${err.message}`,
        );
        console.error(err.stack);
        call.emit('error', {
          code: grpcStatus.INTERNAL,
          message: '[loadConversation]: ' + err.message,
        });
        return;
      }

      if (!conversation.state) {
        console.warn(
          `(${correlationId}): Conversation ${conversation.id} missing state`,
        );
      }

      const botId = req.getBotId();
      let text = req.getMessageText();

      if (botMentioned(botId, text)) {
        text = text.replace(`<@${botId}>`, '').trim();
      }

      // If this is in a slack conversation channel. Let's check if there is a tag setup?
      // If there is, then let's update the settings to be strict
      if (
        conversation.system?.toLowerCase() === 'slack' &&
        conversation.channel
      ) {
        const tag = await getTagsForConversation(schema, conversation.channel);
        if (tag) {
          logDebugMessage(
            `(${correlationId}): [sendMessage] Slack channel ${conversation.channel} has tag ${tag}. Setting strict mode.`,
          );
          settings.strictMode = true;
        }
      }

      // Load the user's identity and store their message.
      const userMessage: ConversationMessage = {
        role: 'user',
        name: coworkerMessageName,
      };

      // Check if we have images and format content accordingly
      const imageInfos =
        req.getImageInfosList()?.map((info) => ({
          dataUrl: info.getDataUrl(),
          mimetype: info.getMimetype(),
        })) || [];

      if (imageInfos.length > 0) {
        // Format as content parts array with text and images
        userMessage.content = [
          { type: 'text', text } as ConversationTextContentPart,
          ...imageInfos.map(
            (info) =>
              ({
                type: 'image_url',
                image_url: { url: info.dataUrl },
              }) as ConversationImageUrlContentPart,
          ),
        ];
      } else {
        // Standard text-only format
        userMessage.content = text;
      }

      // Lock the conversation for processing.
      const lockAcquired = await tryConversationLock(
        schema,
        conversation.conversationId(),
        timeout / 1000 + 1,
      );
      if (!lockAcquired) {
        // If we couldn't get the lock, queue message and return.
        await queueConversationMessage(
          schema,
          conversation.conversationId(),
          userMessage,
        );
        const resp = new ConversationMessageResponse();
        resp.setMessageQueued(true);
        call.write(resp);
        call.end();
        return;
      }

      // Each message needs a conversation detail record.
      const conversationDetail = await insertConversationDetail(
        schema,
        conversation.id.toString() as ConversationId,
        userMessage,
        req.getUserId(),
      );

      let cartridgeArgs: CommonCartridgeArgs = {
        schema,
        userId: user.id,
        client,
        settings,
        documentTypes,
        conversation,
        salesforceConn,
        hubspotClient,
        message: text,
        prompt: promptOverride,
        imageInfos:
          req.getImageInfosList()?.map((info) => ({
            dataUrl: info.getDataUrl(),
            mimetype: info.getMimetype(),
          })) || [],
      };

      let customCartridgeId = (
        req.getCustomCartridgeId() ? req.getCustomCartridgeId() : null
      ) as CartridgeId;
      //Use the cartridge from state if it exists
      const isCartridgeInState =
        conversation.state?.cartridge_enum_value !== null &&
        conversation.state?.cartridge_enum_value !== undefined;

      if (isCartridgeInState) {
        cartridgeName = conversation.state?.cartridge_enum_value;
        customCartridgeId = conversation.state?.cartridge_id;
        console.log(
          `(${correlationId}): [sendMessage] Using cartridge from state - Name: ${cartridgeName}, ID: ${customCartridgeId}`,
        );
      }

      switch (cartridgeName) {
        case CartridgeEnum.RFP_WRITER:
          bidPacketFiles = await getBidPacketFilesForConversation(conversation);
          break;
        case CartridgeEnum.RFP_PROMPT_GENERATOR:
          bidPacketFiles = await getBidPacketFilesForConversation(conversation);
          break;
        case CartridgeEnum.PADD_CHAT:
          bidPacketFiles = await getBidPacketFilesForConversation(conversation);
          break;
        case CartridgeEnum.RFP_RESEARCHER:
          bidPacketFiles = await getBidPacketFilesForConversation(conversation);
          // exemplarFiles = await getExemplarFilesForConversation(conversation);
          break;
      }

      let cartridge = await getCartridge({
        cartridgeEnum: cartridgeName as CartridgeEnum,
        cartridgeId: customCartridgeId,
        schema,
      });
      await cartridge.init(cartridgeArgs);

      let model = agentModel;

      if (settings.agentModelOverride) {
        model = settings.agentModelOverride;
      }

      if (cartridge.model) {
        model = cartridge.model;
      }

      if (conversation.isNew()) {
        //Set the selected cartrige in the conversation state...
        await conversation.setCartridge(cartridgeName, customCartridgeId);

        //...and the first message.
        await updateConversationDetail(
          schema,
          conversationDetail.id as ConversationDetailId,
          {
            cartridge_enum_value: cartridgeName,
            cartridge_id: customCartridgeId,
          },
        );

        await cartridge.newConversationInit(null);

        await extendConversationLock(
          schema,
          conversation.conversationId(),
          timeout / 1000 + 1,
        );
      }

      // Replace this with logic in the conversation / state handler.
      const superRole = isReasoningModel(model) ? 'developer' : 'system';

      const systemPrompt = await cartridge.getPrompt(null);

      const summaryMessage = conversation.summary
        ? [conversation.summaryMessage()]
        : [];
      if (isReasoningModel(model) && conversation.summary) {
        summaryMessage[0].role = superRole;
      }

      //Daniel hates this hacky shit. Replace it.
      const overriddenMessages = conversation.messages.map((m) => {
        return {
          ...m,
          role:
            m.role === 'system' && isReasoningModel(model) ? superRole : m.role,
        };
      });

      // Construct list of messages for LLM.
      const messages = [
          { role: superRole, content: systemPrompt },
          ...summaryMessage,
          ...overriddenMessages,
          userMessage,
        ],
        response = new AgentResponse(conversation.system),
        tools = cartridge.getAvailableTools();

      logDebugMessage(
        `(${correlationId}): [sendMessage] Starting eager RAG...`,
      );

      const activityLogData = {
        client_id: client.id,
        user_id: user.id,
        source_table: tableNames.TABLE_CONVERSATION_DETAIL,
        source_id: '', // Set per-request.
        type: 'conversation',
        event: req.getEvent(),
        details: JSON.stringify({
          inference: req.getInferredTagsList(),
          channel_type: req.getChannelType(),
        }),
        exclude_from_interaction_count: req.getExcludeFromInteractionCount(),
      };
      const slackTelemetry = {
        clientId: client.id as ClientId,
        clientName: client.name,
        correlationId: correlationId,
        isThread: !req.getIsDm(),
        isDM: req.getIsDm(),
        botMentioned: botMentioned(botId, text),
      };

      let responseFormat: ResponseFormat = null;
      if (req.getResponseFormat() && req.getResponseFormat().getType()) {
        responseFormat = {
          type: req.getResponseFormat().getType() as
            | 'json_schema'
            | 'json_object',
        };
        if (req.getResponseFormat().getJsonSchema()) {
          responseFormat.json_schema = JSON.parse(
            req.getResponseFormat().getJsonSchema(),
          );
        }
      }

      try {
        let toProcess: ConversationMessage[] = messages,
          lockReleased: boolean = false;

        let agentLockLoop = 1;
        do {
          const processed = await processAssistantRequests({
            call,
            schema: schema,
            timeout,
            activityLogData,
            tribbleUserId: user.id as UserId,
            conversationId: conversation.conversationId(),
            channel: conversation.channel,
            threadTs: conversation.messageId,
            slackTelemetry,
            metadataFilters: conversation.metadataFilters,
            messages: toProcess,
            helperFuncKey,
            posiHelpersUrl,
            webSearchSites,
            response,
            system: conversation.system,
            tools,
            loopCount: 0,
            model,
            salesforceConn,
            hubspotClient,
            isContentMod,
            responseFormat,
            bidPacketFiles,
            exemplarFiles,
            cartridge,
            cartridgeArgs,
            settings,
          });

          lockReleased = await tryReleaseConversationLock(
            schema,
            conversation.conversationId(),
          );
          if (!lockReleased) {
            const queuedMessages = await consumeMessageQueue(
              schema,
              conversation.conversationId(),
            );
            await Promise.all(
              queuedMessages.map((m) =>
                insertConversationDetail(
                  schema,
                  conversation.conversationId(),
                  m,
                ),
              ),
            );
            toProcess = processed.concat(queuedMessages);
            const resp = new ConversationMessageResponse();
            resp.setDidConsumeQueue(true); //Let the caller know to expect more messages.
            call.write(resp);
          }

          telemetryClient.trackEvent({
            name: constants.EVENT_AGENT_LOCK_LOOP,
            measurements: {
              loop_count: agentLockLoop,
            },
            properties: {
              [constants.AUTH_ID]: user.id,
              [constants.ACCOUNT_ID]: client.id,
              conversation_id: conversation.id,
              correlation_id: correlationId,
              channel_type: req.getChannelType(),
            },
          });
          agentLockLoop++;
        } while (!lockReleased);

        // Close the response stream.
        call.end();

        // Summarize user history if it's gotten too long.
        if (historyMaxLength > 0) {
          try {
            await conversation.summarizeHistory(
              correlationId,
              timeout,
              activityLogData,
              historyMaxLength,
            );
          } catch (err) {
            console.error(
              `(${correlationId}): [chatResponder] summarizeHistory error: ${err.message}`,
            );
            console.error(err.stack);
          }
        }
      } catch (err) {
        call.emit('error', {
          code: grpcStatus.INTERNAL,
          message: '[sendMessage failure]: ' + err.message,
        });
      }
    } catch (err) {
      console.error(`(${correlationId}): [chatResponder] ${err.message}`);
      console.error(err.stack);
      if (!isTokenLengthError(err)) {
        call.emit('error', {
          code: grpcStatus.INTERNAL,
          message: '[catchAll]: ' + err.message,
        });
      }

      await insertActivityLog(
        client.id,
        req.getUserId(),
        'conversation_detail',
        1,
        'conversation',
        'error',
        {},
        {
          channel_type: req.getChannelType(),
          is_thread: !req.getIsDm(),
          platform_user_id: req.getPlatformUserId() || '',
          error_type: isTokenLengthError(err) ? 'token_length' : 'generic',
          error_message: JSON.stringify(err.message),
          correlationId,
        },
        req.getExcludeFromInteractionCount() ?? false,
      );
    }
  },

  getVersion: async (
    call: ServerUnaryCall<VersionRequest, VersionResponse>,
    callback: sendUnaryData<VersionResponse>,
  ) => {
    try {
      const versionData = readVersionFile();
      const response = new VersionResponse();

      if (versionData) {
        response.setServiceName(versionData.serviceName || 'exocomp');
        response.setVersion(versionData.version || 'unknown');
        response.setCommitSha(versionData.commitSha || 'unknown');
        response.setCommitShort(versionData.commitShort || 'unknown');
        response.setCommitDate(versionData.commitDate || 'unknown');
        response.setBuildDate(versionData.buildDate || 'unknown');
        response.setBuildId(versionData.buildId || 'unknown');
        response.setBranch(versionData.branch || 'unknown');
      } else {
        response.setServiceName('exocomp');
        response.setVersion('unknown');
        response.setCommitSha('unknown');
        response.setCommitShort('unknown');
        response.setCommitDate('unknown');
        response.setBuildDate('unknown');
        response.setBuildId('unknown');
        response.setBranch('unknown');
      }

      callback(null, response);
    } catch (error) {
      console.error('Error in GetVersion:', error);
      callback(
        {
          code: grpcStatus.INTERNAL,
          message: 'Failed to retrieve version information',
        },
        null,
      );
    }
  },
};

function isTokenLengthError(error: {
  response: { data: { error: { code: string } } };
}) {
  return (
    error.response?.data?.error?.code == constants.ERROR_CODE_CONTEXT_LENGTH
  );
}

interface AssistantRequestParams {
  call: ServerWritableStream<
    ConversationMessageRequest,
    ConversationMessageResponse
  >;
  schema: ClientSchema; // in req.
  timeout: number; // in req.
  activityLogData: ActivityLogData;
  tribbleUserId: UserId; // in req.
  conversationId: ConversationId; // in req.
  channel: string; // From conversation.
  threadTs: string; // From conversation.
  slackTelemetry: SlackTelemetry;
  metadataFilters: MetadataFilterValue[]; // TODO(cdinn) addToRequest
  messages: ConversationMessage[]; // From conversation records.
  helperFuncKey: string; // from secrets client
  posiHelpersUrl: string; // from secrets client
  webSearchSites: string[]; // from settings
  response: AgentResponse;
  system: string;
  tools: Tool[]; // hard coded
  loopCount?: number; // Init at 0.
  model: string; // from settings
  salesforceConn: SalesforceConnection;
  hubspotClient: HubSpotClient; // HubSpot client connection
  isContentMod: boolean; // from user roles.
  responseFormat: ResponseFormat;
  bidPacketFiles?: FormRecognizedDoc[];
  exemplarFiles?: FormRecognizedDoc[];
  cartridge: Cartridge;
  cartridgeArgs?: Record<string, any>;
  settings: ResolvedSettings;
}

// Recursively process requests to the assistant, including tool calls.
async function processAssistantRequests(
  props: AssistantRequestParams,
): Promise<ConversationMessage[]> {
  const {
    call,
    schema,
    timeout,
    activityLogData,
    tribbleUserId,
    conversationId,
    channel,
    threadTs,
    slackTelemetry,
    metadataFilters,
    helperFuncKey,
    posiHelpersUrl,
    webSearchSites,
    response,
    salesforceConn,
    hubspotClient,
    isContentMod,
    responseFormat,
    bidPacketFiles,
    exemplarFiles,
    cartridgeArgs,
    settings,
  } = props;

  let { messages, system, tools, cartridge, model } = props; // Potentially overridden by cartridge handoff.

  let loopCount = props.loopCount ?? 1;

  const currentStatus = {
    correlationId: slackTelemetry.correlationId,
    loopCount,
    messages: messages.length,
    tool_calls: messages.filter(
      (m) => m.role === 'assistant' && m.tool_calls?.length,
    ).length,
    toolResponses: messages.filter((m) => m.role === 'tool' && m.content)
      .length,
  };
  console.log(JSON.stringify(currentStatus, null, 2));

  const metadata_filter_ids = metadataFilters?.map((mdf) => mdf.id) ?? [];

  //  If we have a tool call that needs
  //  to show UI blocks, we store them here. It's hacky.
  //  I need to think of a better way for
  //  tool calls to show blocks to the user.
  let blockKitToAppend: KnownBlock[] = [];

  const { id: conversationDetailId } = await createConversationDetail(
    schema,
    conversationId,
  );
  activityLogData.source_id = String(conversationDetailId);

  logDebugMessage(
    `Calling LLM with messages:` + JSON.stringify(messages, null, 2),
  );
  introspectMessages(messages, tribbleUserId);

  // We have little hacks in place to overcome a couple tool-related issues.
  // Track these in telemetry so we can monitor and know when they're no longer needed.
  let track_tool_issue_bad_name = {};
  let track_tool_issue_empty_id_name = false;

  let contentStreaming = false; // We are receiving a message in chunks (as opposed to a toolcall, which comes in one go)
  let hybridMessage = false; // If we have a streaming message AND a tool call in the response.

  const request: ChatCompletionParams['request'] = {
    messages: messages.filter((m: ConversationMessage) => m.role),
    model: model,
    temperature: isReasoningModel(model) ? 1 : agentTemp,
    tools: tools,
    ...(cartridge.toolChoice && { tool_choice: cartridge.toolChoice }),
    ...(!isReasoningModel(model) && { parallel_tool_calls: false }),
    ...(responseFormat && { response_format: responseFormat }),
    ...(cartridge.disableParallelToolCalls && { parallel_tool_calls: false }),
    ...(cartridge.maxTokens && { max_tokens: cartridge.maxTokens }),
  };

  // Fetch conversation state to get any timeFilter stored
  const conversation = await getConversationById(schema, conversationId);
  const timeFilterFromState = conversation?.state?.time_filter;

  const qStartTime = new Date(),
    toolCalls: ConversationMessageToolCall[] = [],
    toolInvocations: Promise<ConversationMessage>[] = [],
    stats = await streamChatCompletion(
      {
        schema,
        correlationId: slackTelemetry.correlationId,
        timeout,
        activityLogData,
        resourceUrl: '/openai/deployments/tribble-gpt4/chat/completions',
        request,
        apiVersion: agentApiVersionLatest,
      },
      //#region StreamHandler
      (chunk: ChatCompletionResponse) => {
        // console.log(
        //   '[conversationService] >>>>>>>>>>>>>>>> chunk: ',
        //   JSON.stringify(chunk, null, 2),
        // );
        try {
          if (chunk.choices.length === 0) {
            return;
          }
          const choice = chunk.choices[0];
          if (!choice.messageDelta) {
            return;
          }
          const message = choice.messageDelta;
          if (message.content_chunk) {
            contentStreaming = true; // We are receiving a message in chunks

            // Store chunk text for citation processing.
            response.appendAnswerChunk(message.content_chunk);

            // Send chunk text to caller.
            const resp = new ConversationMessageResponse(),
              update = new AnswerUpdate();
            update.setAnswerPart(message.content_chunk);
            resp.setAnswerUpdate(update);

            // console.log("[conversationService] sending resp part:", JSON.stringify(resp.toObject(), null, 2));
            call.write(resp);
          }

          message.tool_calls.forEach((rawToolCall) => {
            if (contentStreaming) {
              //This toolcall came with streamed chunks. This is a hybrid message!
              hybridMessage = true;
              contentStreaming = false; // Don't need to keep checking this.
            }

            if (rawToolCall.type !== 'function' || !rawToolCall.function) {
              return;
            }

            const toolCallArgs = JSON.parse(rawToolCall.function.arguments),
              toolCallMessage: ConversationMessageToolCall = {
                id: rawToolCall.id.substring(0, 40),
                type: 'function',
                function: rawToolCall.function,
              };

            // Invalid
            if (
              toolCallMessage.id.length === 0 ||
              toolCallMessage.function.name.length === 0
            ) {
              track_tool_issue_empty_id_name = true;
              return;
            }
            //It's tooltime.
            const toolCallLength = toolCalls.push(toolCallMessage);

            const commonToolProps: ToolCommonConfig = {
              //Anything that a tool might need goes in here
              schema,
              tribbleUserId,
              clientId: slackTelemetry.clientId,
              clientName: slackTelemetry.clientName,
              conversationId,
              conversationDetailId:
                conversationDetailId as ConversationDetailId,
              metadataFilters,
              messages,
              activityLogData,
              helperFuncKey,
              posiHelpersUrl,
              salesforceConn,
              hubspotClient,
              isContentMod,
              bidPacketFiles,
              exemplarFiles,
              webSearchSites,
              timeFilter: timeFilterFromState,
              pureMDToken,
              settings,
              agentType: cartridge.getAgentType(),
              logger,
            };

            const toolCall = createToolInstance(
              toolCallMessage.function.name,
              commonToolProps,
              toolCallArgs,
            );

            toolCalls[toolCallLength - 1].do_not_recurse =
              toolCall.do_not_recurse;

            if (!toolCall && toolCallMessage.id.length > 0) {
              toolInvocations.push(
                Promise.resolve({
                  role: 'tool',
                  content:
                    `Error: unknown tool call "${toolCallMessage.function.name}", ` +
                    `valid tools: ${JSON.stringify(tools.map((t) => t['function']['name']))}`,
                  tool_call_id: toolCallMessage.id.substring(0, 40),
                }),
              );
            }

            if (!toolCall) {
              console.log('Unknown tool call:', toolCallMessage);
              return;
            }

            // Kick this off here so tool starts before any Slack tool updates.
            const toolResults = toolCall.performCall();

            toolInvocations.push(
              (async () => {
                const isRecursiveCall = !toolCallMessage.do_not_recurse;
                const isStandardMessage = !hybridMessage;
                const shouldSendToolUpdates = isRecursiveCall;

                try {
                  if (shouldSendToolUpdates) {
                    const pendingResp = new ConversationMessageResponse(),
                      pendingUpdate = new ToolStatusUpdate();
                    pendingUpdate.setToolCallId(toolCallMessage.id);
                    pendingUpdate.setStatus(toolCall.getPendingMessage());
                    pendingResp.setToolStatusUpdate(pendingUpdate);
                    call.write(pendingResp);
                  }

                  let content = await toolResults;
                  if (
                    useTool(
                      toolCallMessage.function.name,
                      LearnFact.tool_name,
                    ) ||
                    useTool(
                      toolCallMessage.function.name,
                      GenerateEphemeralUI.tool_name,
                    ) ||
                    useTool(
                      toolCallMessage.function.name,
                      GenerateResultUI.tool_name,
                    )
                  ) {
                    //We have some blockkit to send back to the caller.
                    //TODO: Daniel - Update tool abstraction to handle sending of blockkit.
                    const completionBlocks = toolCall.getCompletionBlocks({
                      schema,
                      client_id: slackTelemetry.clientId,
                      conversation_detail_id: conversationDetailId,
                      tool_call_id: toolCallMessage.id.substring(0, 40),
                      metadata_filter_ids,
                      ui_id: content ? JSON.parse(content).ui_id : undefined,
                    });

                    if (completionBlocks && completionBlocks.length > 0) {
                      blockKitToAppend =
                        blockKitToAppend.concat(completionBlocks);
                      const answerUpdate = new AnswerUpdate();
                      answerUpdate.setBlocks(JSON.stringify(blockKitToAppend));
                      const resp = new ConversationMessageResponse();
                      resp.setAnswerUpdate(answerUpdate);
                      call.write(resp);
                    }
                  }

                  if (shouldSendToolUpdates) {
                    const completeResp = new ConversationMessageResponse(),
                      completeUpdate = new ToolStatusUpdate();
                    completeUpdate.setToolCallId(toolCallMessage.id);
                    completeUpdate.setStatus(toolCall.getCompletionMessage());
                    completeResp.setToolStatusUpdate(completeUpdate);
                    call.write(completeResp);
                  }

                  //#region tool_truncation
                  const tokensSoFar = conversation.countHistoryTokens();
                  const toolResultTokens = encoding.encode(
                    content || '',
                  ).length;
                  const shouldTruncateToolResult =
                    tokensSoFar + toolResultTokens > globalContextLengthMaximum;

                  if (shouldTruncateToolResult) {
                    console.log(
                      `[toolcallTruncation] ${schema} | conversationId: ${conversation.id} | toolId: ${toolCallMessage.id} | tokensSoFar: ${tokensSoFar} | toolResultTokens: ${toolResultTokens}`,
                    );

                    const truncatedLength =
                      tokensSoFar +
                      toolResultTokens -
                      globalContextLengthMaximum;

                    const truncationStringStart = `<-- The following tool result has been truncated because the length exceeded the system limits -->\n\n`;
                    const truncationStringEnd = `\n<-- tool result truncated -->`;
                    const truncatedContent = content.substring(
                      0,
                      truncatedLength -
                        (truncationStringStart.length +
                          truncationStringEnd.length),
                    );
                    content = `${truncationStringStart}${truncatedContent}${truncationStringEnd}`;
                  }
                  //#endregion tool_truncation

                  const toolMessage = {
                    role: 'tool',
                    content: content,
                    tool_call_id: toolCallMessage.id.substring(0, 40),
                  };
                  await insertConversationDetail(
                    schema,
                    conversationId,
                    toolMessage,
                  );

                  if (shouldTruncateToolResult) {
                    const helperMessage = {
                      role: 'assistant',
                      content: `NOTE: That last tool result was too long and it got truncated.`,
                    };
                    await insertConversationDetail(
                      schema,
                      conversationId,
                      helperMessage,
                    );
                  }

                  if (
                    useTool(
                      toolCallMessage.function.name,
                      ExtractTemporal.tool_name,
                    )
                  ) {
                    // If this is an extract_temporal tool call, save the extracted time filter
                    try {
                      const timeFilter = (
                        toolCall as ExtractTemporal
                      ).getTimeFilter();
                      if (timeFilter) {
                        // Update conversation state with time filter
                        await updateConversationStateTimeFilter(
                          schema,
                          parseInt(conversationId),
                          timeFilter,
                        );
                      }
                    } catch (error) {
                      console.error('Error saving time filter:', error);
                    }
                  }

                  //#region Handoff
                  if (toolCall.is_handoff) {
                    const targetCartridgeEnum =
                      toolCall.getTargetCartrigeForHandoff();

                    if (targetCartridgeEnum !== null) {
                      const newCartridge =
                        getCartridgeByEnum(targetCartridgeEnum);

                      const {
                        messages: newMessages,
                        systemMessage: newSystemMessage,
                        tools: newTools,
                        model: newModel,
                      } = await newCartridge.midConversationInit({
                        conversationId: parseInt(conversationId),
                        messages,
                        cartridgeArgs,
                        conversation,
                      });
                      messages = newMessages;
                      system = newSystemMessage;
                      tools = newTools;
                      cartridge = newCartridge;

                      if (newModel && newModel !== model) {
                        model = newModel;
                      }

                      //Lastly, set the cartridge in state so subsequent messages respect it.
                      await conversation.setCartridge(targetCartridgeEnum);
                    }
                  }
                  //#endregion Handoff

                  return toolMessage;
                } catch (error) {
                  console.error('Error in processing tool call:', error);
                  return {
                    role: 'tool',
                    content: error.toString(),
                    tool_call_id: toolCallMessage.id.substring(0, 40),
                  };
                }
              })(),
            );
          });
        } catch (err) {
          console.error('Error in processing chunk:', err);
          console.error(err.stack);
        }
      },
      //#endregion StreamHandler
    ),
    qEndTime = new Date(),
    qDurationMS = qEndTime.getTime() - qStartTime.getTime(),
    { duration } = stats,
    llmStartTime = new Date(duration.start).getTime(),
    llmEndTime = new Date(duration.end).getTime(),
    llmDurationMs = llmEndTime - llmStartTime;

  logDebugMessage(
    `(${slackTelemetry.correlationId}): [processAssistantRequests] LLM duration: ${llmDurationMs}ms`,
  );

  // Add this message to the conversation history.
  const assistantMessage: ConversationMessage = {
    role: 'assistant',
  };
  if (response.answer) {
    assistantMessage.content = response.answer;
  }
  if (toolCalls.length) {
    assistantMessage.tool_calls = toolCalls;
  }

  let answer = response.answer;

  const allMessages = await getMessagesAfterSeq(schema, conversationId, 0);
  let initialContexts: ConversationSourceFormat[] = [];
  getEagerRagToolResults(allMessages).map((toolResult) => {
    try {
      const parsed = JSON.parse(toolResult.content as string);
      initialContexts = initialContexts.concat(parsed);
    } catch (err) {
      console.error(
        `[ processAssistantRequests ] Error parsing eager rag tool result: ${err}`,
        toolResult.content,
      );
    }
  });

  const usedWebCitations = WebSearch.extractCitations(answer, messages);
  const usedBrainCitations = BrainSearch.extractCitations(
    answer,
    messages,
    initialContexts,
  );
  const usedTieredCitations = TieredContextSearchTool.extractCitations(
    answer,
    messages,
    initialContexts,
  );
  const usedStructCitations = StructuredQuery.extractCitations(
    answer,
    messages,
    initialContexts,
  );

  // If we every need to extract salesforce citations we can do it here.
  // const usedSFCitations = SalesforceQuery.extractCitations(answer);

  response.setAnswer(answer);
  // Must clean up answer text after citations are extracted.
  answer = response.refinedAnswerText();

  // Last Coworker message (this is the last question asked by the user)
  const coworkerMessages = messages.filter(
    (msg) => msg.role === 'user' && msg.name === coworkerMessageName,
  );

  // Only save the citation details (Brain or Web) for an assistant response.
  // (Assistant message = no tool calls OR tool calls with streamed message (hybrid)).
  // These get saved into the 'Sources' column in conversation_detail.
  const shouldSaveCitations = toolCalls.length == 0 || hybridMessage;

  // Similar to above: for assistant response, save the initial user (Coworker)
  // message (the user question) that prompted the agent response. This just saves
  // us a DB trip when popping open the View Details modal, and showing the initial
  // Question (from user) and Answer (from assistant).
  const shouldStoreCoworkerMsg =
    shouldSaveCitations && coworkerMessages.length > 0;

  await completeConversationDetail(
    schema,
    conversationDetailId as ConversationDetailId,
    assistantMessage,
    shouldSaveCitations
      ? {
          brain: usedBrainCitations.citations ?? [],
          web: usedWebCitations.citations ?? [],
          tiered: usedTieredCitations.citations ?? [],
          structured: usedStructCitations.citations ?? [],
        }
      : null,
    shouldStoreCoworkerMsg
      ? coworkerMessages[coworkerMessages.length - 1]
      : null,
    shouldStoreCoworkerMsg ? tribbleUserId : null,
    stats,
  );
  messages.push(assistantMessage);

  console.log(
    `(${slackTelemetry.correlationId}): [processAssistantRequests] 
    
    Message: "${answer}"
    
    
    `,
  );

  const allUsedSources = {
    brain: usedBrainCitations.citations,
    web: usedWebCitations.citations,
    structured: usedStructCitations.citations,
  };

  const validSources = await selectValidEmbeddings(
      schema,
      usedBrainCitations.citations.map((c) => c.id),
    ),
    validIds = validSources.map((source) => source.id);

  await attachConversationDetailEmbeddings(
    schema,
    conversationDetailId as ConversationDetailId,
    validIds,
  );

  const resp = new ConversationMessageResponse(),
    update = new AnswerUpdate();
  update.setIsComplete(true);
  update.setConversationDetailId(conversationDetailId);
  update.setIsHybrid(hybridMessage);
  update.setSources(JSON.stringify(allUsedSources));
  resp.setAnswerUpdate(update);
  call.write(resp);

  console.log(
    `(${slackTelemetry.correlationId}): [processAssistantRequests] Q response: ${JSON.stringify(
      {
        prompt_tokens: stats.token_usage.prompt_tokens,
        completion_tokens: stats.token_usage.completion_tokens,
        llm_duration_ms: llmDurationMs,
        q_overhead_ms: qDurationMS - llmDurationMs,
      },
    )}`,
  );

  //Telemetry
  telemetryClient.trackEvent({
    name: constants.EVENT_LLM_CHAT_CALL,
    measurements: {
      duration: llmDurationMs,
      num_messages: messages.length,
      q_overhead: qDurationMS - llmDurationMs,
      agent_loop_count: loopCount,
    },
    properties: {
      [constants.AUTH_ID]: tribbleUserId || -1,
      [constants.ACCOUNT_ID]: slackTelemetry.clientId,
      source: system,
      input: JSON.stringify(messages),
      //      output: llmResponse,
      is_thread: slackTelemetry.isThread,
      is_dm: slackTelemetry.isDM,
      bot_mentioned: slackTelemetry.botMentioned,
      conversation_detail_id: conversationDetailId,
      conversation_id: conversationId,
      correlation_id: slackTelemetry.correlationId,
      track_tool_issue_bad_name,
      track_tool_issue_empty_id_name,
    },
  });

  // Only recurse when there are tool call results to process.
  if (toolCalls.length) {
    // Check for duplicate tool calls that yields the same results
    const {
      hasTooManyRepeatedToolCallWithSameResults,
      duplicateToolCallsAndResults,
    } = isRepeatedToolCallWithSameResults({
      messages,
      duplicationThreshold: 5,
    });
    if (hasTooManyRepeatedToolCallWithSameResults) {
      console.warn(
        '[processAssistantRequests] Found duplicate tool calls and results! Asking the LLM to stop.. Duplicates:',
        duplicateToolCallsAndResults,
      );

      // Inform the LLM there are too many repeated calls. Ask to try a different approach.
      duplicateToolCallsAndResults.forEach((toolCallGroup) => {
        const latestToolCallInGroup = toolCallGroup[toolCallGroup.length - 1];

        toolInvocations.push(
          Promise.resolve({
            role: 'assistant',
            content: `I've noticed I made repeated calls to ${latestToolCallInGroup.assistantFunctionName} 
              with same arguments (${latestToolCallInGroup.assistantFunctionArgs}). I should try a different approach.`,
            tool_call_id: latestToolCallInGroup.toolCallId,
          }),
        );
      });
    }

    const toolMessages = await Promise.all(toolInvocations);
    messages.push(...toolMessages);
    // Send response to LLM after all tool responses have been collected.
    //A little bit of tracking for tool call errors that get buried in the logs:
    const error13Count = toolMessages.filter((t) =>
      t.content?.toString()?.includes(constants.ERROR_13_INVALID_PROMPT),
    ).length;

    if (error13Count > 0) {
      telemetryClient.trackMetric({
        name: 'error_13_invalid_prompt',
        value: error13Count,
        properties: {
          schema,
          conversation_detail_id: conversationDetailId,
        },
      });
    }

    // For a little magic effect, check for queued messages here too.
    const queuedMessages = await consumeMessageQueue(schema, conversationId);
    await Promise.all(
      queuedMessages.map((m) =>
        insertConversationDetail(schema, conversationId, m),
      ),
    );
    messages.push(...queuedMessages);

    // If all tool calls have do_not_recurse set, we're done.
    if (
      toolCalls.every((tc) => tc.do_not_recurse) &&
      queuedMessages.length == 0
    ) {
      return;
    }

    if (stats.token_usage.total_tokens > forceSummaryIfThisManyTokens) {
      //We are in some crazy agent loop. Force summarization
      try {
        const conversation = await getConversationById(schema, conversationId);
        await conversation.loadHistory();
        await conversation.summarizeHistory(
          slackTelemetry.correlationId,
          timeout,
          activityLogData,
          historyMaxLength,
        );
      } catch (err) {
        console.error(
          `(${slackTelemetry.correlationId}): [chatResponder] forced summarizeHistory error: ${err.message}`,
        );
        console.error(err.stack);
      }
    }

    // End with recursive call to this function to process the next request.
    return processAssistantRequests({
      call,
      schema,
      timeout,
      activityLogData,
      tribbleUserId,
      conversationId,
      channel,
      threadTs,
      slackTelemetry,
      metadataFilters,
      messages,
      helperFuncKey,
      posiHelpersUrl,
      webSearchSites,
      response: new AgentResponse(conversation.system),
      system,
      tools,
      loopCount: loopCount + 1,
      model,
      salesforceConn,
      hubspotClient,
      isContentMod,
      responseFormat,
      bidPacketFiles,
      exemplarFiles,
      cartridge,
      cartridgeArgs,
      settings,
    });
  }

  return messages;
}

function isRepeatedToolCallWithSameResults({
  messages,
  duplicationThreshold = 5,
}: {
  messages: ConversationMessage[];
  duplicationThreshold: number;
}) {
  const relatedMessages = messages.reduce((acc, msg) => {
    if (msg.role === 'tool' && msg.tool_call_id) {
      // Find matching assistant message with tool call
      const matchingAssistant = messages.find(
        (m) =>
          m.role === 'assistant' &&
          m.tool_calls?.some((tc) => tc.id === msg.tool_call_id),
      );
      if (matchingAssistant) {
        acc.push({
          toolCallId: msg.tool_call_id,
          tool: msg,
          toolContent: msg.content,
          assistant: matchingAssistant,
          assistantFunctionName: matchingAssistant.tool_calls?.find(
            (tc) => tc.id === msg.tool_call_id,
          )?.function?.name,
          assistantFunctionArgs: matchingAssistant.tool_calls?.find(
            (tc) => tc.id === msg.tool_call_id,
          )?.function?.arguments,
        });
      }
    }
    return acc;
  }, []);

  const groupedToolCallsAndResults = relatedMessages.reduce((acc, rm) => {
    const key = `${rm.assistantFunctionName}-${rm.assistantFunctionArgs}-${rm.toolContent}`;
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(rm);
    return acc;
  }, {});

  const duplicateToolCallsAndResults: any[] = Object.values(
    groupedToolCallsAndResults,
  ).filter((group: any[]) => group?.length > duplicationThreshold);

  const hasTooManyRepeatedToolCallWithSameResults =
    duplicateToolCallsAndResults.length > 0;

  return {
    hasTooManyRepeatedToolCallWithSameResults,
    duplicateToolCallsAndResults,
  };
}

export function extractDomainName(url: string): string | null {
  let u: URL;
  try {
    u = new URL(url);
  } catch (error) {
    return null;
  }
  return u.hostname;
}

function botMentioned(botId: string, message: string): boolean {
  return message.includes(`@${botId}`);
}

function useTool(llmSelectedName: string, candidateTool: string): boolean {
  llmSelectedName = llmSelectedName.replace('functions.', '');
  return (
    llmSelectedName === candidateTool ||
    llmSelectedName.startsWith(candidateTool)
  );
}

function clientTypeToString(clientType: ClientType): string {
  switch (clientType) {
    case ClientType.CLIENT_TYPE_SLACK:
      return ConversationSystem.SLACK;
    case ClientType.CLIENT_TYPE_TEAMS:
      return ConversationSystem.TEAMS;
    case ClientType.CLIENT_TYPE_WHATSAPP:
      return 'WHATSAPP';
    case ClientType.CLIENT_TYPE_EXTENSION:
      return ConversationSystem.EXTENSION;
    case ClientType.CLIENT_TYPE_WEBCHAT:
      return ConversationSystem.WEBCHAT;
    case ClientType.CLIENT_TYPE_ANSWER_RFP:
      return ConversationSystem.ANSWER_RFP;
    case ClientType.CLIENT_TYPE_UNKNOWN:
    default:
      return 'UNKNOWN';
  }
}

//Log to console if debug flag is true
export const logDebugMessage = (s: string) => {
  if (process.env.DEBUG) {
    console.log(s);
  }
};
