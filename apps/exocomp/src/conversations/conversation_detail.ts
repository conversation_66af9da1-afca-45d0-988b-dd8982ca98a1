import { ClientSchema } from '@tribble/tribble-db';
import { getDB, query } from '@tribble/tribble-db/db_ops';
import {
  ConversationDetailFileId,
  ConversationDetailId,
  ConversationDetailUpdate,
  ConversationId,
} from '@tribble/tribble-db/types';
import { sql } from 'kysely';
import { ConversationContext } from './conversation_context.ts';
import { ConversationMessage } from './message.ts';

const constants = {
  TABLE_CONVERSATION_DETAIL_EMBEDDING: 'conversation_detail_embedding',
};

export interface TokenUsage {
  completion_tokens: number;
  prompt_tokens: number;
  total_cost?: number;
  total_tokens: number;
}

export interface Statistics {
  token_usage: TokenUsage;
  duration?: { start: Date; end: Date };
}

export interface ConversationSourceFormat {
  id: number;
  text: string;
  relevance?: number; // optional for now, but can add back later on if more accurate
  file_name: string;
  url: string;
  reference?: string;
  tags?: any;
  table_json?: any;
  date?: string; //YYYY-MM-DD
  applicability?: string; // websites: internal | partner | competitor
  applicability_label?: string; // if partner or competitor, their name
  document_url?: string; // Web URL of document this content was sourced from.
}

export interface ConversationDetailSources {
  brain: Partial<ConversationSourceFormat>[];
  web: Partial<ConversationSourceFormat>[];
  tiered: Partial<ConversationSourceFormat>[];
  structured: Partial<ConversationSourceFormat>[];
  // salesforce: Partial<ConversationSourceFormat>[];
}

// Create empty conversation_detail record to get an id for the activity log.
export async function createConversationDetail(
  schema: ClientSchema,
  conversationId: ConversationId,
): Promise<{ id: number }> {
  const db = await getDB();
  return await db
    .withSchema(schema)
    .insertInto('conversation_detail')
    .values((eb) => ({
      type: 'agent',
      conversation_id: conversationId,
      cartridge_enum_value: eb
        .selectFrom('conversation_state')
        .select('cartridge_enum_value')
        .where('conversation_id', '=', conversationId),
      cartridge_id: eb
        .selectFrom('conversation_state')
        .select('cartridge_id')
        .where('conversation_id', '=', conversationId),
      seq: eb
        .selectFrom('conversation_detail')
        .select(sql<number>`coalesce(max(seq), 0) + 1`.as('next_seq'))
        .where('conversation_id', '=', conversationId)
        .limit(1),
    }))
    .returning(['id'])
    .executeTakeFirst();
}

export async function updateConversationDetail(
  schema: string,
  conversationDetailId: ConversationDetailId,
  conversationDetail: ConversationDetailUpdate,
) {
  try {
    const db = await getDB();
    return await db
      .withSchema(schema)
      .updateTable('conversation_detail')
      .set(conversationDetail)
      .where('id', '=', conversationDetailId)
      .returning(['id'])
      .executeTakeFirst();
  } catch (err) {
    console.error(
      '[ updateConversationDetail ]Could not update conversation detail',
      err,
    );
  }
}

export async function insertConversationDetail(
  schema: string,
  conversationId: ConversationId,
  message: ConversationMessage,
  userId?: number,
  stats?: Statistics,
): Promise<{ id: number }> {
  const db = await getDB();

  return await db
    .withSchema(schema)
    .insertInto('conversation_detail')
    .values((eb) => ({
      type: 'agent',
      conversation_id: conversationId,
      cartridge_enum_value: eb
        .selectFrom('conversation_state')
        .select('cartridge_enum_value')
        .where('conversation_id', '=', conversationId),
      cartridge_id: eb
        .selectFrom('conversation_state')
        .select('cartridge_id')
        .where('conversation_id', '=', conversationId),
      message,
      seq: eb
        .selectFrom('conversation_detail')
        .select(sql<number>`coalesce(max(seq), 0) + 1`.as('next_seq'))
        .where('conversation_id', '=', conversationId)
        .limit(1),
      statistics: stats ?? {},
      created_date: new Date(),
      user_id: userId,
    }))
    .returning(['id'])
    .executeTakeFirst();
}

export async function completeConversationDetail(
  schema: ClientSchema,
  conversationDetailId: ConversationDetailId,
  message: ConversationMessage,
  sources: ConversationDetailSources,
  input: any,
  userId?: number,
  stats?: Statistics,
): Promise<boolean> {
  const db = await getDB();
  const updateValues: Record<string, any> = {
    message,
    statistics: stats,
    created_date: new Date(),
    sources,
    user_id: userId,
  };

  if (input !== null) {
    updateValues.input = input;
  }

  const result = await db
    .withSchema(schema)
    .updateTable('conversation_detail')
    .set(updateValues)
    .where('id', '=', conversationDetailId)
    .returning('id')
    .execute();
  if (result && result.length) {
    return result[0].id === conversationDetailId;
  } else {
    throw new Error(
      `Failed to complete conversation detail, id ${conversationDetailId} not found`,
    );
  }
}

export async function insertConversationDetailFile(
  schema: string,
  conversationDetailId: ConversationDetailId,
  mimeType: string,
  storageId: string,
): Promise<{ id: ConversationDetailFileId }> {
  const db = await getDB();
  return await db
    .withSchema(schema)
    .insertInto('conversation_detail_file')
    .values((eb) => ({
      mime_type: mimeType,
      storage_id: storageId,
      conversation_detail_id: conversationDetailId,
    }))
    .returning(['id'])
    .executeTakeFirst();
}

export async function attachConversationDetailEmbeddings(
  schema: ClientSchema,
  conversationDetailId: ConversationDetailId,
  embeddingIds: number[],
): Promise<boolean> {
  if (embeddingIds.length === 0) return false;

  const values = [];
  const placeholders = embeddingIds
    .map((_, index) => `($1, $${index + 2})`)
    .join(', ');

  const queryString = `
    INSERT INTO ${schema}.${constants.TABLE_CONVERSATION_DETAIL_EMBEDDING}
    (conversation_detail_id, embedding_id)
    VALUES ${placeholders}
    ON CONFLICT DO NOTHING;
  `;

  values.push(conversationDetailId, ...embeddingIds);

  const result = await query(queryString, values);
  return result.rowCount > 0;
}

export async function insertConversationDetailEmbedding(
  schema: string,
  conversationDetailId: ConversationDetailId,
  records: ConversationContext[],
) {
  const db = await getDB();
  return await db
    .withSchema(schema)
    .insertInto('conversation_detail_embedding')
    .values(
      records.map((context) => {
        return {
          conversation_detail_id: conversationDetailId,
          embedding_id: context.embedding_id,
        };
      }),
    )
    .execute();
}
