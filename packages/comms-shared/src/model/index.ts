import { format } from 'date-fns';
import { MetadataFilterValue } from '../db/queries';

export type ContentType = 'DOC' | 'RFP' | 'URL' | 'SLACK' | 'LEARNED';

export type SlackChannel = {
  id: string;
  name: string;
  created: number;
  creator: string;

  is_im: boolean;
  is_mpim: boolean;
  is_shared: boolean;
  is_channel: boolean;
  is_private: boolean;
  is_archived: boolean;

  //   topic: {
  //     value: string;
  //     creator: string;
  //     last_set: number;
  //   };
};

export type SlackUser = {
  id: string;
  is_bot: boolean;
  team: string;

  email?: string;
  title?: string;
  first_name?: string;
  last_name?: string;
  real_name?: string;

  is_invited_user?: boolean;

  image_24?: string;
  image_32?: string;
  image_48?: string;
  image_72?: string;
  image_192?: string;
  image_512?: string;
  image_1024?: string;
};

export type SlackMessage = {
  ts: string;
  float_ts: number;

  text: string;
  user: string;

  subtype?: string;
  thread_ts?: string; //If this message is a thread header, this will be populated
  channel_id: string;

  reply_count?: number;
  reply_users_count?: number;
  reply_users?: string[];
  latest_reply?: string;
};

export interface ConversationSourceFormat {
  id: number;
  text: string;
  relevance?: number; // optional for now, but can add back later on if more accurate
  file_name: string;
  url: string;
  reference?: string;
  tags?: any;
  table_json?: any;
  date?: string; //YYYY-MM-DD
  applicability?: string; // websites: internal | partner | competitor
  applicability_label?: string; // if partner or competitor, their name
  document_url?: string; // Web URL of document this content was sourced from.
}

const TABLE_MARKER_RE = /\[\[TABLE_\d+\]\][.]/g;

export class ConversationContext {
  text: string;
  embedding_id: number;
  document_id: number;
  file_name: string;
  label: string;
  created_date?: string;
  similarity: number;
  privacy?: 'private' | 'public'; //A duplicate of metadata.privacy!! metadata.privacy is driven off the embedding, this is driven off the document. ¯\_(ツ)_/¯
  source_url?: string;
  metadata: {
    page_numbers?: number[];
    privacy?: 'private' | 'public' | undefined;
    row_index?: number;
    //Table processing:
    original_text?: string;
    table_data?: any;
    prior_chunk_last_sentence?: string;
    next_chunk_first_sentence?: string;
    //Slack:
    slack_link?: string;
    channel?: SlackChannel;
    users?: SlackUser[];
    messages?: SlackMessage[];
    is_thread?: boolean;
    date?: string; //YYYY-MM-DD. For the LLM.
  };
  snippet_index: number;
  content_type: ContentType;
  document_type_name?: string;
  multiplier: number;
  metadata_filter?: Record<number, string[]>;
  //Table processing:
  table_text?: string;
  table_json?: any;
  // websites: internal | partner | competitor
  applicability?: string;
  // if partner or competitor, their name
  applicability_label?: string;
  external_doc_source?: string;

  constructor(params: any) {
    this.text = params.text;
    this.embedding_id = params.embedding_id;
    this.document_id = params.document_id;
    this.file_name = params.file_name;
    this.label = params.label;
    this.similarity = params.similarity;
    this.privacy = params.privacy;
    this.source_url = params.source_url;
    this.metadata = params.metadata;
    this.snippet_index = params.snippet_index;
    this.content_type = params.content_type;
    this.document_type_name = params.document_type_name;
    this.multiplier = params.multiplier;
    this.metadata_filter = params.metadata_filter;
    this.table_json = params.table_json;
    this.table_text = params.table_text;
    this.created_date = params.created_date;
    this.applicability = params.applicability;
    this.applicability_label = params.applicability_label;
    this.external_doc_source = params.external_doc_source;

    this.translateTables();
  }

  getRelevanceScore(): number {
    // Check if multiplier is NaN and set it to 1 if true
    const useMultiplier = isNaN(this.multiplier) ? 1 : this.multiplier;

    return Math.round(this.similarity * useMultiplier * 100);
  }

  // Returns a simplified JSON object for delivery to and use by an LLM.
  toSourceFormat(
    idx: number,
    allMDF: MetadataFilterValue[],
  ): ConversationSourceFormat {
    const applicability = this.applicability || 'internal';
    const applicability_label_prompt = this.applicability_label
      ? `, ${this.applicability_label}`
      : '';
    const url_file_name = this.file_name.startsWith('https://');

    const out = {
      id: this.embedding_id,
      text:
        (applicability == 'internal'
          ? ''
          : `This context is applicable for one of our ${applicability}s${applicability_label_prompt}: `) +
        this.text,
      // removing for now, but can add back later on if more accurate
      // relevance: this.getRelevanceScore(),
      file_name:
        url_file_name || this.content_type === 'LEARNED'
          ? this.label
          : this.file_name,
      // Removing applicability for now. If we ever get it working, add back here + in agentic prompt
      // applicability,
    } as ConversationSourceFormat;

    if (this.external_doc_source?.length > 0 && this.source_url) {
      out.url = this.source_url;
    } else if (url_file_name) {
      out.url = this.file_name;
    } else {
      out.url = `${process.env.APP_PATH}/sources/source/${this.document_id}`;
      if ((this.metadata.page_numbers?.length ?? 0) === 0) {
        out.url += `/fact/${this.embedding_id}`;
      } else {
        out.url += `?page=${this.metadata.page_numbers[0]}`;
      }
    }

    // If we have a source URL, include it.
    if (this.source_url) {
      out.document_url = this.source_url;
    }

    if (this.metadata.page_numbers) {
      out.reference = `p. ${this.metadata.page_numbers.join(', ')}`;
    } else if (this.metadata.row_index) {
      out.reference = `row ${this.metadata.row_index}`;
    }

    if (this.content_type === 'SLACK') {
      out.file_name = (this.label.startsWith('#') ? '' : '#') + this.label;
      if (this.metadata.slack_link) out.url = this.metadata.slack_link;
      out.date = this.metadata.date;
      out.reference = `Slack message${this.metadata.date ? ' from ' + this.metadata.date : ''}`;
    } else if (this.created_date) {
      try {
        const date = new Date(this.created_date);
        out.date = format(date, 'yyyy-MM-dd');

        // Use the date as the reference, if nothing else is set.
        if (!out.reference) {
          out.reference = format(date, 'yyyy-MM-dd');
        }
      } catch (e) {
        console.warn('Error parsing date', e.message);
        //No big deal if we can't parse the date.
      }
    }

    if (this.metadata_filter != null) {
      //add tags to source, but keyed off name instead of id
      const sourceTags = {};

      Object.entries(this.metadata_filter).forEach(([key, value]) => {
        const typeName = allMDF?.find((mdf) => mdf.type_id == Number(key))?.type
          .name;
        if (typeName) sourceTags[typeName] = value;
      });

      out.tags = sourceTags;
    }

    if (this.table_json) {
      out.table_json = this.table_json;
    }

    return out;
  }

  translateTables() {
    let originalText = this.metadata.original_text || '';

    if (originalText === '') {
      //No tables, continue.
      return;
    }

    let tableMarkers =
      originalText.match(new RegExp(TABLE_MARKER_RE, 'g')) || [];

    if (tableMarkers.length === 0) {
      //No tables!
      return;
    }

    for (let tableMarker of tableMarkers) {
      let tableIdentifier = tableMarker
        .replace(/\[\[/, '')
        .replace(/\]\]\./, ''); //Note: the orignal markers need the '.' so spacy can do chunk ("sentence") seperation.

      let tableData = this.metadata.table_data || {};

      if (tableData[tableIdentifier] === undefined) {
        continue;
      }

      let tableCaption =
        '[TABLE SUMMARY: ' + tableData[tableIdentifier].caption + ']';
      let tableJsonDefinition = tableData[tableIdentifier].metadata;

      let prefix = this.metadata.prior_chunk_last_sentence || '';
      let suffix = this.metadata.next_chunk_first_sentence || '';

      this.text = prefix + ' ' + tableCaption + ' ' + suffix;
      this.table_text =
        '[TABLE TEXT: ' + tableData[tableIdentifier].text.trim() + ']';
      this.table_json = tableJsonDefinition;
    }
  }
}

export interface ConversationImageUrl {
  // url property may contain base64 encoded image data.
  url: string;
  detail?: string;
}

export interface ConversationImageUrlContentPart {
  type: 'image_url';
  image_url: ConversationImageUrl;
}

export interface ConversationTextContentPart {
  type: 'text';
  text: string;
}

export type ConversationContentPart =
  | ConversationImageUrlContentPart
  | ConversationTextContentPart;

export interface ConversationMessageToolCall {
  id: string;
  type: string;
  function: {
    name: string;
    arguments: string;
  };
  do_not_recurse?: boolean;
}

export interface ConversationMessage {
  role: string;
  content?: string | ConversationContentPart[];
  name?: string;
  tool_calls?: ConversationMessageToolCall[];
  tool_call_id?: string;
}

export interface ConversationState {
  id?: number;
  conversation_id: number;
  metadata_filter?: number[];
  time_filter?: {
    startDate?: string; // ISO date format
    endDate?: string; // ISO date format
    timeRangeText?: string; // Original text description
    sortOrder?: 'newest' | 'oldest' | 'not_applicable';
  };
  cartridge_enum_value?: number;
  created_date?: Date;
}

export interface ConversationDetailSources {
  brain: Partial<ConversationSourceFormat>[];
  web: Partial<ConversationSourceFormat>[];
  tiered: Partial<ConversationSourceFormat>[];
  // salesforce: Partial<ConversationSourceFormat>[];
}

export interface User {
  id: number;
  client_id: number;
  external_id?: string;
  external_system: string;
  name: string;
  is_admin: boolean;
  is_tribble: boolean;
  is_deleted: boolean;
  deleted_by_id?: number;
  deleted_date?: Date;
  status?: 'active' | 'terminated';
  email: string;
}
